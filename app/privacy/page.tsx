import Link from "next/link"
import { ArrowLeft } from "lucide-react"

export default function PrivacyPage() {
  return (
    <div className="min-h-screen bg-white py-12">
      <div className="container mx-auto px-4">
        <Link href="/" className="inline-flex items-center text-pink-500 hover:text-pink-600 mb-8">
          <ArrowLeft className="mr-2 h-4 w-4" /> Back to Home
        </Link>

        <div className="max-w-3xl mx-auto">
          <h1 className="text-3xl md:text-4xl font-bold mb-8">Privacy Policy</h1>

          <div className="prose prose-pink max-w-none">
            <p className="lead text-xl text-gray-600 mb-8">
              We take your privacy seriously. This policy explains how we collect, use, and protect your data.
            </p>

            <h2>Data Collection</h2>
            <p>When you use our AI Wedding Photo Generator, we collect the following information:</p>
            <ul>
              <li>Photos you upload for processing</li>
              <li>Generated wedding photos</li>
              <li>Basic usage information (time spent, features used)</li>
            </ul>

            <h2>How We Use Your Data</h2>
            <p>Your uploaded photos and generated images are used solely for:</p>
            <ul>
              <li>Creating your AI-generated wedding photos</li>
              <li>Improving our AI algorithms (with your consent)</li>
              <li>Troubleshooting technical issues</li>
            </ul>

            <h2>Data Security</h2>
            <p>We implement strong security measures to protect your data:</p>
            <ul>
              <li>All uploaded photos are encrypted in transit and at rest</li>
              <li>Your original photos are automatically deleted after 24 hours</li>
              <li>Generated photos are stored for 24 hours unless you create an account</li>
              <li>We never share your photos with third parties without your explicit consent</li>
            </ul>

            <h2>Your Rights</h2>
            <p>You have the right to:</p>
            <ul>
              <li>Access your data</li>
              <li>Delete your data</li>
              <li>Opt out of AI model training</li>
              <li>Request a copy of your data</li>
            </ul>

            <h2>Contact Us</h2>
            <p>
              If you have any questions about our privacy practices, please contact us at:
              <br />
              <a href="mailto:<EMAIL>" className="text-pink-500 hover:underline">
                <EMAIL>
              </a>
            </p>

            <p className="text-sm text-gray-500 mt-8">Last updated: June 13, 2025</p>
          </div>
        </div>
      </div>
    </div>
  )
}
