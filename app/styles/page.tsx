"use client"

import { useState } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { ArrowLeft, ArrowRight, Check } from "lucide-react"
import { useRouter } from "next/navigation"

const WEDDING_STYLES = [
  {
    id: "chinese-traditional",
    name: "Chinese Traditional",
    description: "Classic Chinese wedding attire with traditional elements",
    image: "/placeholder.svg?height=300&width=300",
  },
  {
    id: "western-elegant",
    name: "Western Elegant",
    description: "Sophisticated Western-style wedding with formal attire",
    image: "/placeholder.svg?height=300&width=300",
  },
  {
    id: "beach-sunset",
    name: "Beach Sunset",
    description: "Romantic beach wedding during golden hour",
    image: "/placeholder.svg?height=300&width=300",
  },
  {
    id: "forest-fairy",
    name: "Forest Fairy Tale",
    description: "Enchanted forest setting with magical elements",
    image: "/placeholder.svg?height=300&width=300",
  },
  {
    id: "vintage-classic",
    name: "Vintage Classic",
    description: "Timeless vintage style with retro elements",
    image: "/placeholder.svg?height=300&width=300",
  },
  {
    id: "modern-minimalist",
    name: "Modern Minimalist",
    description: "Clean, contemporary style with minimalist aesthetics",
    image: "/placeholder.svg?height=300&width=300",
  },
]

export default function StylesPage() {
  const [selectedStyles, setSelectedStyles] = useState<string[]>([])
  const router = useRouter()

  const toggleStyle = (styleId: string) => {
    if (selectedStyles.includes(styleId)) {
      setSelectedStyles(selectedStyles.filter((id) => id !== styleId))
    } else {
      setSelectedStyles([...selectedStyles, styleId])
    }
  }

  const handleContinue = () => {
    if (selectedStyles.length > 0) {
      // In a real app, you would save the selected styles
      // For now, we'll just navigate to the generation page
      router.push("/generating")
    }
  }

  const handleRandomSelection = () => {
    // Select 3 random styles
    const shuffled = [...WEDDING_STYLES].sort(() => 0.5 - Math.random())
    const randomStyles = shuffled.slice(0, 3).map((style) => style.id)
    setSelectedStyles(randomStyles)
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-pink-50 py-12">
      <div className="container mx-auto px-4">
        <Link href="/upload" className="inline-flex items-center text-pink-500 hover:text-pink-600 mb-8">
          <ArrowLeft className="mr-2 h-4 w-4" /> Back to Upload
        </Link>

        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl md:text-4xl font-bold text-center mb-4">Choose Your Wedding Styles</h1>
          <p className="text-gray-600 text-center mb-8">
            Select one or more styles for your AI-generated wedding photos. You can choose up to 3 styles.
          </p>

          <div className="mb-6 text-center">
            <Button
              variant="outline"
              className="border-pink-500 text-pink-500 hover:bg-pink-50"
              onClick={handleRandomSelection}
            >
              Surprise Me (Random Selection)
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {WEDDING_STYLES.map((style) => (
              <Card
                key={style.id}
                className={`overflow-hidden cursor-pointer transition-all ${
                  selectedStyles.includes(style.id) ? "ring-2 ring-pink-500 ring-offset-2" : "hover:shadow-lg"
                }`}
                onClick={() => toggleStyle(style.id)}
              >
                <div className="relative">
                  <img src={style.image || "/placeholder.svg"} alt={style.name} className="w-full h-48 object-cover" />
                  {selectedStyles.includes(style.id) && (
                    <div className="absolute top-2 right-2 bg-pink-500 text-white p-1 rounded-full">
                      <Check className="h-5 w-5" />
                    </div>
                  )}
                </div>
                <div className="p-4">
                  <h3 className="font-bold mb-1">{style.name}</h3>
                  <p className="text-sm text-gray-600">{style.description}</p>
                </div>
              </Card>
            ))}
          </div>

          <div className="text-center">
            <p className="text-gray-600 mb-6">
              {selectedStyles.length === 0
                ? "Please select at least one style"
                : `You've selected ${selectedStyles.length} ${selectedStyles.length === 1 ? "style" : "styles"}`}
              {selectedStyles.length >= 3 && " (maximum reached)"}
            </p>

            <Button
              className="bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white px-8 py-6 rounded-xl text-lg font-medium"
              disabled={selectedStyles.length === 0}
              onClick={handleContinue}
            >
              Generate Wedding Photos <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
