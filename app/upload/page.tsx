"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { ArrowLeft, Upload, ImageIcon, AlertCircle } from "lucide-react"
import { useRouter } from "next/navigation"

export default function UploadPage() {
  const [dragActive, setDragActive] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()

    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0])
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFile(e.target.files[0])
    }
  }

  const handleFile = (file: File) => {
    // Check if file is an image
    if (!file.type.match("image.*")) {
      setError("Please upload an image file (JPEG, PNG, etc.)")
      return
    }

    // Check file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      setError("File size should be less than 10MB")
      return
    }

    setError(null)
    setSelectedFile(file)

    // Create preview
    const reader = new FileReader()
    reader.onloadend = () => {
      setPreviewUrl(reader.result as string)
    }
    reader.readAsDataURL(file)
  }

  const handleContinue = () => {
    if (selectedFile) {
      // In a real app, you might upload the file here
      // For now, we'll just navigate to the style selection page
      router.push("/styles")
    } else {
      setError("Please select an image first")
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-pink-50 py-12">
      <div className="container mx-auto px-4">
        <Link href="/" className="inline-flex items-center text-pink-500 hover:text-pink-600 mb-8">
          <ArrowLeft className="mr-2 h-4 w-4" /> Back to Home
        </Link>

        <div className="max-w-2xl mx-auto">
          <h1 className="text-3xl md:text-4xl font-bold text-center mb-8">Upload Your Photo</h1>
          <p className="text-gray-600 text-center mb-8">
            Upload a clear portrait photo for the best results. We recommend using a front-facing photo with good
            lighting.
          </p>

          <Card className="p-8 shadow-lg">
            <div
              className={`border-2 border-dashed rounded-xl p-8 text-center ${dragActive ? "border-pink-500 bg-pink-50" : "border-gray-300"}`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              {previewUrl ? (
                <div className="mb-4">
                  <img
                    src={previewUrl || "/placeholder.svg"}
                    alt="Preview"
                    className="max-h-64 mx-auto rounded-lg object-contain"
                  />
                  <p className="mt-4 text-sm text-gray-500">{selectedFile?.name}</p>
                </div>
              ) : (
                <div className="py-8">
                  <div className="mb-4 flex justify-center">
                    <ImageIcon className="h-16 w-16 text-gray-400" />
                  </div>
                  <p className="text-gray-600 mb-4">Drag and drop your photo here, or click to select</p>
                  <p className="text-sm text-gray-500">Supported formats: JPEG, PNG, HEIC</p>
                  <p className="text-sm text-gray-500">Maximum size: 10MB</p>
                </div>
              )}

              <input type="file" id="file-upload" className="hidden" accept="image/*" onChange={handleFileChange} />

              <div className="mt-6">
                <label htmlFor="file-upload">
                  <Button
                    variant="outline"
                    className="border-pink-500 text-pink-500 hover:bg-pink-50"
                    onClick={() => document.getElementById("file-upload")?.click()}
                  >
                    <Upload className="mr-2 h-4 w-4" />
                    {previewUrl ? "Choose Another Photo" : "Select Photo"}
                  </Button>
                </label>
              </div>
            </div>

            {error && (
              <div className="mt-4 p-3 bg-red-50 text-red-700 rounded-lg flex items-start">
                <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
                <span>{error}</span>
              </div>
            )}

            <div className="mt-8">
              <Button
                className="w-full bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white py-6 rounded-xl text-lg font-medium"
                disabled={!selectedFile}
                onClick={handleContinue}
              >
                Continue to Style Selection
              </Button>
            </div>
          </Card>

          <div className="mt-8 text-center text-sm text-gray-500">
            <p>
              By uploading, you agree to our{" "}
              <Link href="/privacy" className="text-pink-500 hover:underline">
                Privacy Policy
              </Link>{" "}
              and{" "}
              <Link href="/terms" className="text-pink-500 hover:underline">
                Terms of Service
              </Link>
              .
            </p>
            <p className="mt-2">Your photos are processed securely and deleted after 24 hours.</p>
          </div>
        </div>
      </div>
    </div>
  )
}
