"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { ArrowLeft, Download, Share2, Heart, Facebook, Instagram, Twitter, Copy, Check } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

const GENERATED_PHOTOS = [
  {
    id: 1,
    style: "Chinese Traditional",
    image: "/placeholder.svg?height=600&width=400",
  },
  {
    id: 2,
    style: "Western Elegant",
    image: "/placeholder.svg?height=600&width=400",
  },
  {
    id: 3,
    style: "Beach Sunset",
    image: "/placeholder.svg?height=600&width=400",
  },
]

export default function ResultsPage() {
  const [favorites, setFavorites] = useState<number[]>([])
  const [copied, setCopied] = useState<number | null>(null)

  const toggleFavorite = (id: number) => {
    if (favorites.includes(id)) {
      setFavorites(favorites.filter((favId) => favId !== id))
    } else {
      setFavorites([...favorites, id])
    }
  }

  const handleDownload = (image: string, style: string) => {
    // In a real app, this would trigger a download
    // For now, we'll just log it
    console.log(`Downloading ${style} image`)

    // Create a fake download link
    const link = document.createElement("a")
    link.href = image
    link.download = `wedding-photo-${style.toLowerCase().replace(" ", "-")}.png`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const handleCopyLink = (id: number) => {
    // In a real app, this would copy a shareable link
    // For now, we'll just simulate it
    navigator.clipboard.writeText(`https://ai-wedding-photos.com/share/${id}`)
    setCopied(id)
    setTimeout(() => setCopied(null), 2000)
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-pink-50 py-12">
      <div className="container mx-auto px-4">
        <Link href="/" className="inline-flex items-center text-pink-500 hover:text-pink-600 mb-8">
          <ArrowLeft className="mr-2 h-4 w-4" /> Back to Home
        </Link>

        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl md:text-4xl font-bold text-center mb-4">Your Wedding Photos</h1>
          <p className="text-gray-600 text-center mb-12">
            Here are your AI-generated wedding photos. Download in high resolution or share with friends!
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {GENERATED_PHOTOS.map((photo) => (
              <Card key={photo.id} className="overflow-hidden">
                <div className="relative">
                  <img
                    src={photo.image || "/placeholder.svg"}
                    alt={`${photo.style} wedding photo`}
                    className="w-full h-80 object-cover"
                  />
                  <button
                    className={`absolute top-4 right-4 p-2 rounded-full ${
                      favorites.includes(photo.id)
                        ? "bg-pink-500 text-white"
                        : "bg-white/80 text-gray-600 hover:bg-white"
                    }`}
                    onClick={() => toggleFavorite(photo.id)}
                  >
                    <Heart className="h-5 w-5" fill={favorites.includes(photo.id) ? "currentColor" : "none"} />
                  </button>
                </div>

                <div className="p-4">
                  <h3 className="font-bold mb-3">{photo.style}</h3>

                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      className="flex-1 border-pink-500 text-pink-500 hover:bg-pink-50"
                      onClick={() => handleDownload(photo.image, photo.style)}
                    >
                      <Download className="mr-2 h-4 w-4" />
                      Download
                    </Button>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" className="border-gray-300">
                          <Share2 className="mr-2 h-4 w-4" />
                          Share
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem className="cursor-pointer">
                          <Facebook className="mr-2 h-4 w-4 text-blue-600" />
                          Facebook
                        </DropdownMenuItem>
                        <DropdownMenuItem className="cursor-pointer">
                          <Instagram className="mr-2 h-4 w-4 text-pink-600" />
                          Instagram
                        </DropdownMenuItem>
                        <DropdownMenuItem className="cursor-pointer">
                          <Twitter className="mr-2 h-4 w-4 text-blue-400" />
                          Twitter
                        </DropdownMenuItem>
                        <DropdownMenuItem className="cursor-pointer" onClick={() => handleCopyLink(photo.id)}>
                          {copied === photo.id ? (
                            <>
                              <Check className="mr-2 h-4 w-4 text-green-500" />
                              Copied!
                            </>
                          ) : (
                            <>
                              <Copy className="mr-2 h-4 w-4" />
                              Copy Link
                            </>
                          )}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </Card>
            ))}
          </div>

          <div className="text-center space-y-6">
            <Button
              className="bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white px-8 py-6 rounded-xl text-lg font-medium"
              onClick={() => (window.location.href = "/upload")}
            >
              Create More Wedding Photos
            </Button>

            <p className="text-sm text-gray-500">
              Your photos will be stored securely for 24 hours. Download them now to keep them forever.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
