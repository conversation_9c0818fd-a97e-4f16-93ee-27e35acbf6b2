import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowRight, Heart, Shield, Zap } from "lucide-react"

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-pink-50">
      {/* Hero Section */}
      <section className="container mx-auto px-4 pt-20 pb-16 text-center">
        <div className="max-w-3xl mx-auto">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Transform Your Photos Into <span className="text-pink-500">Beautiful Wedding Memories</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8">
            Upload your selfie and let our AI create stunning wedding photos in various styles - Chinese, Western,
            beach, forest, and more.
          </p>
          <Link href="/upload">
            <Button className="bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white px-8 py-6 rounded-full text-lg font-medium">
              Create Your Wedding Photos <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>

        <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow">
            <div className="bg-pink-100 p-4 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <Zap className="h-8 w-8 text-pink-500" />
            </div>
            <h3 className="text-xl font-bold mb-2">Fast Generation</h3>
            <p className="text-gray-600">See your wedding photos in seconds, not hours. No more waiting anxiety.</p>
          </div>

          <div className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow">
            <div className="bg-pink-100 p-4 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <Heart className="h-8 w-8 text-pink-500" />
            </div>
            <h3 className="text-xl font-bold mb-2">Multiple Styles</h3>
            <p className="text-gray-600">
              Choose from various wedding styles, backgrounds, and themes for your perfect look.
            </p>
          </div>

          <div className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow">
            <div className="bg-pink-100 p-4 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <Shield className="h-8 w-8 text-pink-500" />
            </div>
            <h3 className="text-xl font-bold mb-2">Privacy Protected</h3>
            <p className="text-gray-600">Your photos are secure and private. We delete all uploads after processing.</p>
          </div>
        </div>
      </section>

      {/* Example Results Section */}
      <section className="container mx-auto px-4 py-16 text-center">
        <h2 className="text-3xl md:text-4xl font-bold mb-12">See What Our AI Can Create</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <div key={i} className="rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow">
              <img
                src={`/placeholder.svg?height=400&width=300`}
                alt={`Example wedding photo ${i}`}
                className="w-full h-80 object-cover"
              />
              <div className="p-4 bg-white">
                <p className="font-medium text-gray-900">
                  {i % 3 === 0 ? "Chinese Traditional" : i % 3 === 1 ? "Western Elegant" : "Beach Sunset"} Style
                </p>
              </div>
            </div>
          ))}
        </div>
        <div className="mt-12">
          <Link href="/upload">
            <Button className="bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white px-8 py-6 rounded-full text-lg font-medium">
              Create Your Own Now <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>
      </section>

      {/* How It Works */}
      <section className="container mx-auto px-4 py-16 bg-white rounded-3xl shadow-lg my-16">
        <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center">How It Works</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="text-center">
            <div className="bg-pink-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl font-bold text-pink-500">1</span>
            </div>
            <h3 className="text-xl font-bold mb-2">Upload Photo</h3>
            <p className="text-gray-600">Upload your selfie or portrait photo</p>
          </div>

          <div className="text-center">
            <div className="bg-pink-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl font-bold text-pink-500">2</span>
            </div>
            <h3 className="text-xl font-bold mb-2">Choose Style</h3>
            <p className="text-gray-600">Select from multiple wedding styles</p>
          </div>

          <div className="text-center">
            <div className="bg-pink-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl font-bold text-pink-500">3</span>
            </div>
            <h3 className="text-xl font-bold mb-2">AI Generation</h3>
            <p className="text-gray-600">Our AI transforms your photo</p>
          </div>

          <div className="text-center">
            <div className="bg-pink-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl font-bold text-pink-500">4</span>
            </div>
            <h3 className="text-xl font-bold mb-2">Download & Share</h3>
            <p className="text-gray-600">Get high-quality results to share</p>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-50 py-12">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-6 md:mb-0">
              <h2 className="text-2xl font-bold text-pink-500">AI Wedding Photos</h2>
              <p className="text-gray-600 mt-2">Create beautiful memories in seconds</p>
            </div>
            <div className="flex space-x-6">
              <Link href="/privacy" className="text-gray-600 hover:text-pink-500">
                Privacy Policy
              </Link>
              <Link href="/terms" className="text-gray-600 hover:text-pink-500">
                Terms of Service
              </Link>
              <Link href="/contact" className="text-gray-600 hover:text-pink-500">
                Contact Us
              </Link>
            </div>
          </div>
          <div className="border-t border-gray-200 mt-8 pt-8 text-center text-gray-500">
            <p>© {new Date().getFullYear()} AI Wedding Photos. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
