"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { X } from "lucide-react"

export default function GeneratingPage() {
  const [progress, setProgress] = useState(0)
  const router = useRouter()

  useEffect(() => {
    // Simulate AI generation progress
    const interval = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 100) {
          clearInterval(interval)
          // Navigate to results page when complete
          setTimeout(() => {
            router.push("/results")
          }, 500)
          return 100
        }
        return prev + 5
      })
    }, 300)

    return () => clearInterval(interval)
  }, [router])

  const handleCancel = () => {
    // In a real app, you would cancel the generation process
    router.push("/upload")
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-pink-50 flex items-center justify-center">
      <div className="container max-w-md mx-auto px-4 text-center">
        <div className="mb-8">
          <div className="relative w-64 h-64 mx-auto mb-8">
            {/* Animated wedding dress icon */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-32 h-32 rounded-full bg-pink-100 animate-pulse flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="64"
                  height="64"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-pink-500"
                >
                  <path d="M12 10c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2s2 .9 2 2v4c0 1.1-.9 2-2 2z" />
                  <path d="M6 16c0-3.3 2.7-6 6-6s6 2.7 6 6v6h-3v-6c0-1.7-1.3-3-3-3s-3 1.3-3 3v6H6v-6z" />
                </svg>
              </div>
            </div>

            {/* Animated particles */}
            <div className="absolute inset-0">
              {[...Array(8)].map((_, i) => (
                <div
                  key={i}
                  className="absolute w-3 h-3 bg-pink-300 rounded-full animate-float"
                  style={{
                    left: `${Math.random() * 100}%`,
                    top: `${Math.random() * 100}%`,
                    animationDelay: `${Math.random() * 2}s`,
                    animationDuration: `${3 + Math.random() * 3}s`,
                  }}
                />
              ))}
            </div>
          </div>

          <h1 className="text-3xl font-bold mb-4">Creating Your Wedding Photos</h1>
          <p className="text-gray-600 mb-8">Our AI is working its magic. This may take a minute...</p>

          <div className="mb-4">
            <Progress value={progress} className="h-3" />
          </div>

          <p className="text-sm text-gray-500 mb-8">
            {progress < 30 && "Analyzing your photo..."}
            {progress >= 30 && progress < 60 && "Applying wedding styles..."}
            {progress >= 60 && progress < 90 && "Adding final touches..."}
            {progress >= 90 && "Almost ready..."}
          </p>

          <Button variant="outline" className="border-gray-300 text-gray-500 hover:bg-gray-50" onClick={handleCancel}>
            <X className="mr-2 h-4 w-4" />
            Cancel
          </Button>
        </div>
      </div>

      <style jsx global>{`
        @keyframes float {
          0%, 100% {
            transform: translateY(0) scale(1);
            opacity: 0;
          }
          50% {
            transform: translateY(-20px) scale(1.5);
            opacity: 0.8;
          }
          100% {
            transform: translateY(-40px) scale(1);
            opacity: 0;
          }
        }
        .animate-float {
          animation: float 3s infinite;
        }
      `}</style>
    </div>
  )
}
