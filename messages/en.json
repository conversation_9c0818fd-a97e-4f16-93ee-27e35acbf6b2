{"template": "flux-ai-template", "theme": "dark", "header": {"logo": "Your Logo", "nav": {"introduce": "Features", "benefit": "Benefits", "showcase": "Showcase", "stats": "Stats", "pricing": "Pricing", "testimonial": "Testimonials", "faq": "FAQ"}, "cta": {"login": "<PERSON><PERSON>", "signup": "Get Started"}, "userMenu": {"myOrders": "My Orders", "signOut": "Sign Out", "profile": "My Profile"}}, "hero": {"title": "Transform Your Imagination Into Images", "subtitle": "Watch as your words bloom into stunning visuals with Flux AI Image Generator", "description": "State-of-the-art AI image generation platform with enhanced detail, improved prompt adherence, and diverse outputs", "cta": {"primary": "Start For Free", "secondary": "View Gallery"}}, "branding": {"title": "Trusted by Industry Leaders", "subtitle": "Join thousands of companies already using our platform", "brands": [{"name": "Company 1", "logo": "/brands/1.svg"}, {"name": "Company 2", "logo": "/brands/2.svg"}, {"name": "Company 3", "logo": "/brands/3.svg"}]}, "introduce": {"title": "Advanced AI Image Generation", "subtitle": "Powerful Features for Creative Minds", "description": "Generate high-quality images from text descriptions using our cutting-edge AI technology", "image": "/features/intro.gif", "features": [{"title": "Enhanced Image Detail", "description": "Generate images with exceptional detail and clarity using advanced AI algorithms", "icon": "sparkles"}, {"title": "Improved Prompt Adherence", "description": "A<PERSON>ura<PERSON>y interprets and executes your text prompts for precise results", "icon": "wand"}, {"title": "Increased Output Diversity", "description": "Explore various interpretations and styles from similar prompts", "icon": "palette"}]}, "benefit": {"title": "Why Choose Us", "subtitle": "Benefits that set us apart", "benefits": [{"title": "Increased Productivity", "description": "Build and deploy faster with automated workflows", "icon": "speed"}, {"title": "Better Collaboration", "description": "Work together seamlessly with built-in collaboration tools", "icon": "team"}, {"title": "Enhanced Security", "description": "Enterprise-grade security to protect your code and data", "icon": "shield"}]}, "usage": {"title": "How It Works", "subtitle": "Get started in three simple steps", "steps": [{"title": "Create Your Project", "description": "Set up your project with our intuitive project creation wizard", "image": "/usage/step1.png"}, {"title": "Configure <PERSON><PERSON>s", "description": "Customize your project settings to match your needs", "image": "/usage/step2.png"}, {"title": "Start Development", "description": "Begin coding with our powerful development tools", "image": "/usage/step3.png"}]}, "feature": {"title": "Available Models", "subtitle": "Choose the perfect model for your needs", "items": [{"title": "Flux Pro", "description": "Flagship model optimized for high-end commercial use", "icon": "star"}, {"title": "Flux 1.1 Pro Ultra", "description": "4K resolution generator optimized for high-speed generation", "icon": "bolt"}, {"title": "Flux Schnell", "description": "Turbo mode for fastest image generation", "icon": "rocket"}]}, "showcase": {"title": "See It in Action", "subtitle": "Real projects built with our platform", "gallery": [{"title": "Project 1", "description": "A modern web application", "image": "/gallery/1.jpg"}, {"title": "Project 2", "description": "Mobile app development", "image": "/gallery/2.jpg"}, {"title": "Project 3", "description": "Enterprise solution", "image": "/gallery/3.jpg"}]}, "stats": {"title": "By the Numbers", "subtitle": "Our impact in the development community", "stats": [{"value": "10M+", "label": "Downloads", "description": "Active developers using our platform"}, {"value": "50K+", "label": "Projects", "description": "Successfully completed projects"}, {"value": "99.9%", "label": "Uptime", "description": "Platform reliability"}, {"value": "24/7", "label": "Support", "description": "Always here to help"}]}, "pricing": {"title": "Choose Your Plan", "subtitle": "Start creating amazing images today", "perMonth": "/month", "contactUs": "Contact Us", "getStarted": "Get Started", "buyNow": "Buy Now", "pleaseLogin": "Please log in to continue with your purchase", "plans": [{"name": "Basic", "price": "$9.99", "amount": 9.99, "description": "Perfect for individuals", "features": ["100 images per month", "Basic image generation", "Email support", "HD resolution (1024x1024)", "Access to Flux Basic model"]}, {"name": "Pro", "price": "$29.99", "amount": 29.99, "description": "For creative professionals", "features": ["500 images per month", "Advanced image generation", "Priority support", "4K resolution", "Access to all Flux models", "Custom image styles", "Batch processing"]}, {"name": "Enterprise", "price": "Contact Us", "description": "For large organizations", "features": ["Unlimited images", "Custom API access", "24/7 dedicated support", "Custom resolution options", "Private model training", "SLA guarantee", "Custom integration support"]}]}, "testimonial": {"title": "What Our Users Say", "subtitle": "Don't just take our word for it", "testimonials": [{"content": "This platform has transformed how we build software. The productivity gains are incredible.", "author": {"name": "<PERSON>", "title": "CTO", "company": "Tech Corp", "image": "/testimonials/1.jpg"}}, {"content": "The best development platform we've used. The features and support are unmatched.", "author": {"name": "<PERSON>", "title": "Lead Developer", "company": "Dev Inc", "image": "/testimonials/2.jpg"}}, {"content": "A game-changer for our development team. We've cut our development time in half.", "author": {"name": "<PERSON>", "title": "Engineering Manager", "company": "Software Co", "image": "/testimonials/3.jpg"}}]}, "faq": {"title": "Frequently Asked Questions", "subtitle": "Find answers to common questions", "faqs": [{"question": "How do I get started?", "answer": "Sign up for a free account and follow our quick start guide. You'll be up and running in minutes."}, {"question": "What kind of support do you offer?", "answer": "We offer various support options including community forums, documentation, and dedicated support for paid plans."}, {"question": "Can I use it with my existing tools?", "answer": "Yes! Our platform integrates with most popular development tools and services."}]}, "auth": {"signInTitle": "Sign in to your account", "signInWithGoogle": "Continue with Google", "signInWithGithub": "Continue with GitHub", "signingIn": "Signing in...", "oauthError": "An error occurred during sign in. Please try again.", "authError": "Authentication error. Please try again.", "signIn": "Sign in", "email": "Email address", "password": "Password", "confirmPassword": "Confirm password", "noAccount": "Don't have an account? Sign up", "alreadyHaveAccount": "Already have an account? Sign in", "orContinueWith": "Or continue with", "invalidCredentials": "Invalid email or password", "signInError": "An error occurred during sign in. Please try again.", "signUp": "Sign up", "signingUp": "Signing up...", "createAccount": "Create an account", "signUpError": "An error occurred during sign up. Please try again.", "passwordsDoNotMatch": "Passwords do not match", "signUpSuccess": "Registration successful! Signing you in..."}, "cta": {"title": "Ready to Get Started?", "subtitle": "Join thousands of developers already using our platform", "cta": {"primary": "Start Free Trial", "secondary": "Contact Sales"}}, "footer": {"copyright": "All rights reserved", "about": {"title": "About", "about": "About Us", "blog": "Blog"}, "support": {"title": "Support", "helpCenter": "Help Center", "contactUs": "Contact Us"}, "legal": {"title": "Legal", "privacy": "Privacy Policy", "terms": "Terms of Service"}, "language": {"title": "Language", "english": "English", "chinese": "中文", "japanese": "日本語"}}, "orders": {"title": "My Orders", "description": "View all your order history and status", "noOrders": "No orders found", "orderDetails": {"purchase": "Purchase", "orderId": "Order ID", "amount": "Amount", "orderDate": "Order Date", "paidDate": "Payment Date", "status": {"paid": "Paid", "pending": "Pending", "failed": "Failed", "expired": "Expired"}}}, "profile": {"title": "My Profile", "subtitle": "View and manage your profile", "personalInfo": "Personal Information", "uuid": "UUID", "email": "Email", "nickname": "Nickname", "registrationDate": "Registration Date", "signinMethod": "Sign-in Method", "lastSigninIp": "Last Sign-in IP", "unknown": "Unknown"}}