"use client";

import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { usePathname } from "next/navigation";

interface CTAProps {
  section: {
    title: string;
    subtitle: string;
    cta: {
      primary: string;
      secondary: string;
    };
  };
}

export function CTA({ section }: CTAProps) {
  const pathname = usePathname();
  const currentLocale = pathname.split('/')[1];

  return (
    <section className="py-20">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold mb-6">{section.title}</h2>
          <p className="text-xl text-gray-600 mb-8">{section.subtitle}</p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href={`/${currentLocale}/upload`}>
              <Button size="lg" className="bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white rounded-full px-8">
                {section.cta.primary}
              </Button>
            </Link>
            <Link href={`/${currentLocale}/results`}>
              <Button
                size="lg"
                variant="outline"
                className="rounded-full px-8 border-pink-500 text-pink-500 hover:bg-pink-50"
              >
                {section.cta.secondary}
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}
