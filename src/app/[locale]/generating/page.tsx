"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useTranslations } from "next-intl";

export default function GeneratingPage() {
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState(0);
  const router = useRouter();
  const t = useTranslations("photoAI.generating");

  const steps = t.raw("steps") as string[];

  useEffect(() => {
    // Check if we have the required data
    const uploadedPhoto = sessionStorage.getItem("uploadedPhoto");
    const selectedStyles = sessionStorage.getItem("selectedStyles");
    
    if (!uploadedPhoto || !selectedStyles) {
      router.push("/upload");
      return;
    }

    // Simulate AI generation progress
    const interval = setInterval(() => {
      setProgress((prev) => {
        const newProgress = prev + Math.random() * 8 + 2; // Random increment between 2-10
        
        // Update current step based on progress
        if (newProgress >= 25 && currentStep < 1) setCurrentStep(1);
        if (newProgress >= 50 && currentStep < 2) setCurrentStep(2);
        if (newProgress >= 75 && currentStep < 3) setCurrentStep(3);
        
        if (newProgress >= 100) {
          clearInterval(interval);
          // Navigate to results page when complete
          setTimeout(() => {
            router.push("/results");
          }, 1000);
          return 100;
        }
        return newProgress;
      });
    }, 300);

    return () => clearInterval(interval);
  }, [router, currentStep]);

  const handleCancel = () => {
    // Clear session storage and redirect
    sessionStorage.removeItem("uploadedPhoto");
    sessionStorage.removeItem("selectedStyles");
    router.push("/upload");
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-pink-50 flex items-center justify-center">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto text-center">
          <Card className="p-8 relative overflow-hidden">
            {/* Animated background */}
            <div className="absolute inset-0 bg-gradient-to-r from-pink-50 to-rose-50 opacity-50">
              <div className="absolute inset-0">
                {[...Array(6)].map((_, i) => (
                  <div
                    key={i}
                    className="absolute w-4 h-4 bg-pink-300 rounded-full animate-pulse"
                    style={{
                      left: `${Math.random() * 100}%`,
                      top: `${Math.random() * 100}%`,
                      animationDelay: `${Math.random() * 2}s`,
                      animationDuration: `${2 + Math.random() * 2}s`,
                    }}
                  />
                ))}
              </div>
            </div>

            {/* Content */}
            <div className="relative z-10">
              {/* AI Icon Animation */}
              <div className="mb-8">
                <div className="w-20 h-20 mx-auto bg-gradient-to-r from-pink-500 to-rose-500 rounded-full flex items-center justify-center animate-pulse">
                  <svg
                    className="w-10 h-10 text-white animate-spin"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                </div>
              </div>

              <h1 className="text-3xl font-bold mb-4">{t("title")}</h1>
              <p className="text-gray-600 mb-2">{t("subtitle")}</p>
              <p className="text-gray-500 text-sm mb-8">{t("description")}</p>

              {/* Progress Bar */}
              <div className="mb-8">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-gray-700">{t("progress")}</span>
                  <span className="text-sm font-medium text-pink-600">{Math.round(progress)}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div
                    className="bg-gradient-to-r from-pink-500 to-rose-500 h-3 rounded-full transition-all duration-300 ease-out"
                    style={{ width: `${progress}%` }}
                  />
                </div>
              </div>

              {/* Current Step */}
              <div className="mb-8">
                <div className="space-y-3">
                  {steps.map((step, index) => (
                    <div
                      key={index}
                      className={`flex items-center space-x-3 p-3 rounded-lg transition-all duration-300 ${
                        index === currentStep
                          ? "bg-pink-100 text-pink-700"
                          : index < currentStep
                          ? "bg-green-100 text-green-700"
                          : "bg-gray-100 text-gray-500"
                      }`}
                    >
                      <div
                        className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                          index === currentStep
                            ? "bg-pink-500 text-white animate-pulse"
                            : index < currentStep
                            ? "bg-green-500 text-white"
                            : "bg-gray-300 text-gray-600"
                        }`}
                      >
                        {index < currentStep ? "✓" : index + 1}
                      </div>
                      <span className="text-sm font-medium">{step}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Cancel Button */}
              <Button
                variant="outline"
                onClick={handleCancel}
                className="border-gray-300 text-gray-600 hover:bg-gray-50"
              >
                {t("cancel")}
              </Button>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
