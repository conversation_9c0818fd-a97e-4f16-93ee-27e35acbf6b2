"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ArrowLeft, Upload, AlertCircle } from "lucide-react";
import { useTranslations } from "next-intl";

export default function UploadPage() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const router = useRouter();
  const t = useTranslations("photoAI.upload");

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFile(e.target.files[0]);
    }
  };

  const handleFile = (file: File) => {
    // Check if file is an image
    if (!file.type.match("image.*")) {
      setError(t("uploadError"));
      return;
    }

    // Check file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      setError(t("fileSizeError"));
      return;
    }

    setError(null);
    setSelectedFile(file);

    // Create preview
    const reader = new FileReader();
    reader.onloadend = () => {
      setPreviewUrl(reader.result as string);
    };
    reader.readAsDataURL(file);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    const files = e.dataTransfer.files;
    if (files && files[0]) {
      handleFile(files[0]);
    }
  };

  const handleContinue = () => {
    if (selectedFile) {
      // Store the file in sessionStorage for the next page
      const reader = new FileReader();
      reader.onloadend = () => {
        sessionStorage.setItem("uploadedPhoto", reader.result as string);
        // Get current locale from pathname
        const currentLocale = window.location.pathname.split('/')[1];
        router.push(`/${currentLocale}/styles`);
      };
      reader.readAsDataURL(selectedFile);
    } else {
      setError(t("selectFirst"));
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-pink-50 py-12">
      <div className="container mx-auto px-4">
        <Link href="../" className="inline-flex items-center text-pink-500 hover:text-pink-600 mb-8">
          <ArrowLeft className="mr-2 h-4 w-4" /> {t("backToHome")}
        </Link>

        <div className="max-w-2xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold mb-4">{t("title")}</h1>
            <p className="text-gray-600 text-lg">{t("subtitle")}</p>
            <p className="text-gray-500 mt-2">{t("description")}</p>
          </div>

          <Card className="p-8">
            <div
              className={`border-2 border-dashed rounded-xl p-8 text-center transition-colors ${
                isDragging
                  ? "border-pink-500 bg-pink-50"
                  : previewUrl
                  ? "border-green-500 bg-green-50"
                  : "border-gray-300 hover:border-pink-400"
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              {previewUrl ? (
                <div className="space-y-4">
                  <img
                    src={previewUrl}
                    alt="Preview"
                    className="max-w-full max-h-64 mx-auto rounded-lg shadow-md"
                  />
                  <p className="text-green-600 font-medium">
                    {selectedFile?.name}
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  <Upload className="mx-auto h-12 w-12 text-gray-400" />
                  <div>
                    <p className="text-lg font-medium text-gray-900 mb-2">
                      {t("dragDrop")}
                    </p>
                    <p className="text-sm text-gray-500">{t("supportedFormats")}</p>
                    <p className="text-sm text-gray-500">{t("maxSize")}</p>
                  </div>
                </div>
              )}

              <input
                type="file"
                id="file-upload"
                className="hidden"
                accept="image/*"
                onChange={handleFileChange}
              />

              <div className="mt-6">
                <label htmlFor="file-upload">
                  <Button
                    variant="outline"
                    className="border-pink-500 text-pink-500 hover:bg-pink-50"
                    onClick={() => document.getElementById("file-upload")?.click()}
                  >
                    <Upload className="mr-2 h-4 w-4" />
                    {previewUrl ? t("chooseAnother") : t("selectPhoto")}
                  </Button>
                </label>
              </div>
            </div>

            {error && (
              <div className="mt-4 p-3 bg-red-50 text-red-700 rounded-lg flex items-start">
                <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
                <span>{error}</span>
              </div>
            )}

            <div className="mt-8">
              <Button
                className="w-full bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white py-6 rounded-xl text-lg font-medium"
                disabled={!selectedFile}
                onClick={handleContinue}
              >
                {t("continue")}
              </Button>
            </div>
          </Card>

          <div className="mt-8 text-center text-sm text-gray-500">
            <p>{t("privacyNote")}</p>
            <p className="mt-2">{t("securityNote")}</p>
          </div>
        </div>
      </div>
    </div>
  );
}
